#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test UI functionality của Flask app
"""

import requests
import time
import sys
import os
from dotenv import load_dotenv

# Set encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

# Load environment variables
load_dotenv()

def test_ui_endpoints():
    """Test các endpoint của UI"""
    base_url = "http://127.0.0.1:5000"
    
    print("TESTING UI FUNCTIONALITY")
    print("=" * 50)
    
    # Test GET request
    try:
        response = requests.get(base_url, timeout=10)
        if response.status_code == 200:
            print("✅ GET / - UI loads successfully")
            
            # Check if Azure status is displayed
            if "Azure Authentication Status" in response.text:
                print("✅ Azure authentication status displayed")
            else:
                print("❌ Azure authentication status not found")
                
            # Check if all service forms are present
            services = ["keyvault", "sql", "cosmos", "blob", "acr", "redis"]
            for service in services:
                if f'name="service" value="{service}"' in response.text:
                    print(f"✅ {service.upper()} form present")
                else:
                    print(f"❌ {service.upper()} form missing")
        else:
            print(f"❌ GET / failed with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ GET / failed: {e}")
        return False
    
    # Test Redis functionality if configured
    redis_conn_str = os.getenv('REDIS_CONNECTION_STRING')
    if redis_conn_str:
        print("\n--- Testing Redis UI functionality ---")
        try:
            # Test Redis set operation
            data = {
                'service': 'redis',
                'action': 'add',
                'redis_key': 'test_ui_key',
                'redis_value': 'test_ui_value'
            }
            response = requests.post(base_url, data=data, timeout=30)
            if response.status_code == 200:
                if "set successfully" in response.text or "thành công" in response.text:
                    print("✅ Redis SET via UI successful")
                else:
                    print("❌ Redis SET via UI failed")
                    # Look for error messages in the response
                    if "results_redis" in response.text:
                        import re
                        redis_results = re.search(r'results_redis.*?alert.*?>(.*?)</div>', response.text, re.DOTALL)
                        if redis_results:
                            print("Redis result:", redis_results.group(1).strip())
                    else:
                        print("No Redis results found in response")
            else:
                print(f"❌ Redis POST failed with status {response.status_code}")
                
            # Test Redis list operation
            data = {
                'service': 'redis',
                'action': 'list'
            }
            response = requests.post(base_url, data=data, timeout=30)
            if response.status_code == 200:
                if "connection successful" in response.text or "thành công" in response.text:
                    print("✅ Redis LIST via UI successful")
                else:
                    print("❌ Redis LIST via UI failed")
                    print("Response content:", response.text[:500])
            else:
                print(f"❌ Redis LIST POST failed with status {response.status_code}")
                
        except Exception as e:
            print(f"❌ Redis UI test failed: {e}")
    else:
        print("\n--- Skipping Redis UI test (not configured) ---")
    
    # Test Key Vault functionality if configured
    keyvault_url = os.getenv('KEY_VAULT_URL')
    if keyvault_url:
        print("\n--- Testing Key Vault UI functionality ---")
        try:
            # Test Key Vault list operation
            data = {
                'service': 'keyvault',
                'action': 'list'
            }
            response = requests.post(base_url, data=data, timeout=30)
            if response.status_code == 200:
                if "Secret" in response.text or "thành công" in response.text or "success" in response.text:
                    print("✅ Key Vault LIST via UI successful")
                else:
                    print("❌ Key Vault LIST via UI failed")
                    print("Response content:", response.text[:500])
            else:
                print(f"❌ Key Vault LIST POST failed with status {response.status_code}")
                
        except Exception as e:
            print(f"❌ Key Vault UI test failed: {e}")
    else:
        print("\n--- Skipping Key Vault UI test (not configured) ---")
    
    print("\n" + "=" * 50)
    print("UI FUNCTIONALITY TEST COMPLETED")
    return True

def wait_for_server():
    """Wait for Flask server to be ready"""
    base_url = "http://127.0.0.1:5000"
    max_attempts = 10
    
    for i in range(max_attempts):
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print(f"✅ Server is ready after {i+1} attempts")
                return True
        except:
            pass
        
        print(f"Waiting for server... attempt {i+1}/{max_attempts}")
        time.sleep(2)
    
    print("❌ Server not ready after maximum attempts")
    return False

if __name__ == "__main__":
    print("Waiting for Flask server to start...")
    if wait_for_server():
        test_ui_endpoints()
    else:
        print("❌ Cannot connect to Flask server")
        sys.exit(1)
