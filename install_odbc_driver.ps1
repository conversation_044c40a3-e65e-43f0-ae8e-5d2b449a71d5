# Script để cài đặt Microsoft ODBC Driver 18 for SQL Server
# Ch<PERSON><PERSON> vớ<PERSON> quyền Administrator

Write-Host "Cài đặt Microsoft ODBC Driver 18 for SQL Server..." -ForegroundColor Green

# URL download cho ODBC Driver 18
$downloadUrl = "https://go.microsoft.com/fwlink/?linkid=2249006"
$installerPath = "$env:TEMP\msodbcsql.msi"

try {
    # Tải file installer
    Write-Host "Đang tải ODBC Driver 18..." -ForegroundColor Yellow
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    
    # Cài đặt driver
    Write-Host "Đang cài đặt ODBC Driver 18..." -ForegroundColor Yellow
    Start-Process msiexec.exe -ArgumentList "/i", $installerPath, "/quiet", "/norestart" -Wait
    
    # Kiểm tra cài đặt
    Write-Host "Kiểm tra driver đã cài đặt..." -ForegroundColor Yellow
    $drivers = Get-OdbcDriver | Where-Object {$_.Name -like "*ODBC Driver*SQL Server*"}
    
    if ($drivers) {
        Write-Host "✅ Cài đặt thành công!" -ForegroundColor Green
        Write-Host "Các ODBC drivers có sẵn:" -ForegroundColor Cyan
        $drivers | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
    } else {
        Write-Host "❌ Không tìm thấy driver sau khi cài đặt" -ForegroundColor Red
    }
    
    # Xóa file installer
    Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
    
} catch {
    Write-Host "❌ Lỗi khi cài đặt: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nHoàn tất!" -ForegroundColor Green
