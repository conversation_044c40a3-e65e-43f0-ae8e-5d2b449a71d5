#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test for Redis UI functionality
"""

import requests
import sys
import os
from dotenv import load_dotenv

# Set encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

# Load environment variables
load_dotenv()

def test_redis_ui():
    """Test Redis UI functionality"""
    base_url = "http://127.0.0.1:5000"
    
    print("TESTING REDIS UI")
    print("=" * 30)
    
    # Check if Redis is configured
    redis_conn_str = os.getenv('REDIS_CONNECTION_STRING')
    if not redis_conn_str:
        print("❌ REDIS_CONNECTION_STRING not configured")
        return False
    
    print(f"✅ Redis connection string configured: {redis_conn_str[:50]}...")
    
    # Test Redis SET operation
    print("\n--- Testing Redis SET ---")
    data = {
        'service': 'redis',
        'action': 'add',
        'redis_key': 'test_ui_key',
        'redis_value': 'test_ui_value'
    }
    
    try:
        response = requests.post(base_url, data=data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            # Save full response to file for debugging
            with open('redis_response.html', 'w', encoding='utf-8') as f:
                f.write(response.text)
            print("✅ Response saved to redis_response.html")
            
            # Check for success indicators
            if "set successfully" in response.text.lower():
                print("✅ Redis SET successful")
            elif "thành công" in response.text:
                print("✅ Redis SET successful (Vietnamese)")
            elif "error" in response.text.lower():
                print("❌ Redis SET failed - error found in response")
            else:
                print("⚠️ Redis SET status unclear")
                
            # Look for Redis results section
            if 'results_redis' in response.text:
                print("✅ Redis results section found")
            else:
                print("❌ Redis results section not found")
                
        else:
            print(f"❌ HTTP error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    # Test Redis LIST operation
    print("\n--- Testing Redis LIST ---")
    data = {
        'service': 'redis',
        'action': 'list'
    }
    
    try:
        response = requests.post(base_url, data=data, timeout=30)
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            if "connection successful" in response.text.lower():
                print("✅ Redis LIST successful")
            elif "thành công" in response.text:
                print("✅ Redis LIST successful (Vietnamese)")
            elif "error" in response.text.lower():
                print("❌ Redis LIST failed - error found in response")
            else:
                print("⚠️ Redis LIST status unclear")
        else:
            print(f"❌ HTTP error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_redis_ui()
