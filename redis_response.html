
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Azure Connectivity Tester (Flask)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .card { margin-bottom: 2rem; }
        .service-title { font-size: 1.3rem; font-weight: 600; }
        .result-list { margin-top: 1rem; }
        .azure-status { 
            background: #e9ecef; 
            padding: 1rem; 
            border-radius: 0.375rem; 
            margin-bottom: 2rem; 
            border-left: 4px solid #6c757d;
        }
        .azure-status.success { 
            background: #d1e7dd; 
            border-left-color: #198754; 
        }
        .azure-status.warning { 
            background: #fff3cd; 
            border-left-color: #ffc107; 
        }
    </style>
</head>
<body>
<div class="container py-4">
    <h1 class="mb-4 text-center">🔗 Azure Connectivity Tester (Flask)</h1>
    
    <!-- Azure Authentication Status -->
    <div class="azure-status success">
        <strong>🔐 Azure Authentication Status:</strong> ✅ Azure authentication successful | Configured services: SQL Database, Cosmos DB, Blob Storage, Redis Cache, Key Vault, Container Registry
    </div>
    
    <div class="row row-cols-1 row-cols-md-2 g-4">
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Key Vault</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="keyvault">
                        <div class="col-md-5">
                            <label class="form-label">Secret Name</label>
                            <input name="keyvault_secret_name" class="form-control">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Secret Value</label>
                            <input name="keyvault_secret_value" class="form-control">
                        </div>
                        <div class="col-md-2 d-grid gap-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Add</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Azure SQL Database</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="sql">
                        <div class="col-md-5">
                            <label class="form-label">Table Name</label>
                            <input name="sql_table" class="form-control">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Value</label>
                            <input name="sql_value" class="form-control">
                        </div>
                        <div class="col-md-2 d-grid gap-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Add</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Cosmos DB</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="cosmos">
                        <div class="col-md-4">
                            <label class="form-label">DB Name</label>
                            <input name="cosmos_db" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Container Name</label>
                            <input name="cosmos_container" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Item (JSON)</label>
                            <input name="cosmos_item" class="form-control">
                        </div>
                        <div class="col-12 d-grid gap-2 mt-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Add</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Blob Storage</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="blob">
                        <div class="col-md-4">
                            <label class="form-label">Container Name</label>
                            <input name="blob_container" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Blob Name</label>
                            <input name="blob_name" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Data</label>
                            <input name="blob_data" class="form-control">
                        </div>
                        <div class="col-12 d-grid gap-2 mt-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Upload</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Azure Container Registry (ACR)</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="acr">
                        <div class="col-12 d-grid gap-2">
                            <button type="submit" name="action" value="list" class="btn btn-secondary">List Images</button>
                        </div>
                    </form>
                    
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Azure Redis Cache</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="redis">
                        <div class="col-md-5">
                            <label class="form-label">Key</label>
                            <input name="redis_key" class="form-control">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Value</label>
                            <input name="redis_value" class="form-control">
                        </div>
                        <div class="col-md-2 d-grid gap-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Set</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    
                        <div class="result-list">
                        
                            
                                <div class="alert alert-success py-2 mb-2">✅ Key &#39;test_ui_key&#39; set successfully.</div>
                            
                        
                        </div>
                    
                </div>
            </div>
        </div>
    </div>
    <footer class="text-center mt-4 mb-2 text-muted">&copy; 2024 Azure Connectivity Tester</footer>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>