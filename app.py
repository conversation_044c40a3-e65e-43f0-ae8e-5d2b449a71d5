from flask import Flask, render_template_string, request
import uuid
from azure.identity import DefaultAzureCredential
from azure.keyvault.secrets import SecretClient
from azure.storage.blob import BlobServiceClient
from azure.cosmos import CosmosClient, PartitionKey
from azure.mgmt.containerregistry import ContainerRegistryManagementClient
from azure.mgmt.containerregistry.models import Registry
from azure.mgmt.sql import SqlManagementClient
from azure.mgmt.redis import RedisManagementClient
from azure.mgmt.storage import StorageManagementClient
import pyodbc
import redis
from dotenv import load_dotenv

import os

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)


# --- Helper functions ---
def test_key_vault_full(vault_url, credential):
    steps = []
    try:
        client = SecretClient(vault_url=f"https://{vault_url}/", credential=credential)
        secret_name = f"test-conn-{uuid.uuid4().hex[:8]}"
        secret_value = uuid.uuid4().hex
        client.set_secret(secret_name, secret_value)
        steps.append((True, f"Tạo secret '{secret_name}' thành công"))
        got = client.get_secret(secret_name)
        if got.value == secret_value:
            steps.append((True, f"Đọc secret thành công: {got.value}"))
        else:
            steps.append((False, "Giá trị secret không khớp!"))
        poller = client.begin_delete_secret(secret_name)
        poller.wait()
        steps.append((True, "Xóa secret thành công"))
    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_azure_sql_full(connection_string):
    steps = []
    table = "test_connectivity"
    try:
        conn = pyodbc.connect(connection_string, timeout=5)
        cursor = conn.cursor()
        try:
            cursor.execute(f"CREATE TABLE {table} (id INT PRIMARY KEY, val NVARCHAR(100))")
            conn.commit()
            steps.append((True, "Tạo bảng test thành công"))
        except Exception:
            steps.append((True, "Bảng test đã tồn tại"))
        cursor.execute(f"INSERT INTO {table} (id, val) VALUES (?, ?)", (1, "hello"))
        conn.commit()
        steps.append((True, "Insert thành công"))
        cursor.execute(f"SELECT val FROM {table} WHERE id=1")
        row = cursor.fetchone()
        if row and row[0] == "hello":
            steps.append((True, f"Select thành công: {row[0]}"))
        else:
            steps.append((False, "Select thất bại!"))
        cursor.execute(f"DELETE FROM {table} WHERE id=1")
        conn.commit()
        steps.append((True, "Xóa dòng test thành công"))
        cursor.execute(f"DROP TABLE {table}")
        conn.commit()
        steps.append((True, "Xóa bảng test thành công"))
        conn.close()
    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_cosmosdb_mongodb_api(mongodb_conn_str):
    """Test Cosmos DB với MongoDB API"""
    steps = []
    try:
        from pymongo import MongoClient

        # Tạo MongoDB client
        client = MongoClient(mongodb_conn_str)

        # Test connection
        client.admin.command('ping')
        steps.append((True, "Kết nối MongoDB API thành công"))

        # Tạo database và collection
        db_name = f"testdb{uuid.uuid4().hex[:6]}"
        collection_name = f"testcol{uuid.uuid4().hex[:6]}"

        db = client[db_name]
        collection = db[collection_name]

        # Insert document
        doc = {"_id": "test1", "value": "hello mongodb"}
        result = collection.insert_one(doc)
        steps.append((True, f"Insert document thành công: {result.inserted_id}"))

        # Query document
        found_doc = collection.find_one({"_id": "test1"})
        if found_doc and found_doc["value"] == "hello mongodb":
            steps.append((True, f"Query thành công: {found_doc['value']}"))
        else:
            steps.append((False, "Query thất bại"))

        # Delete document
        collection.delete_one({"_id": "test1"})
        steps.append((True, "Xóa document thành công"))

        # Drop collection và database
        collection.drop()
        steps.append((True, "Xóa collection thành công"))

        client.drop_database(db_name)
        steps.append((True, "Xóa database thành công"))

        client.close()

    except ImportError:
        steps.append((False, "pymongo không được cài đặt. Chạy: pip install pymongo"))
    except Exception as e:
        steps.append((False, str(e)))

    return steps

def test_cosmosdb_full(connection_string):
    """Test Cosmos DB với SQL API hoặc MongoDB API tự động detect"""
    steps = []

    # Nếu là MongoDB connection string, sử dụng MongoDB API
    if connection_string.startswith('mongodb://'):
        return test_cosmosdb_mongodb_api(connection_string)

    # Ngược lại, sử dụng SQL API
    db_name = f"testdb{uuid.uuid4().hex[:6]}"
    container_name = f"testct{uuid.uuid4().hex[:6]}"
    try:
        # Parse connection string để lấy endpoint và key
        conn_parts = dict(part.split('=', 1) for part in connection_string.split(';') if '=' in part)
        endpoint = conn_parts.get('AccountEndpoint', '').replace('https://', '').replace('http://', '')
        key = conn_parts.get('AccountKey', '')

        if not endpoint or not key:
            steps.append((False, "Connection string không hợp lệ"))
            return steps

        client = CosmosClient(f"https://{endpoint}/", key)
        db = client.create_database(db_name)
        steps.append((True, f"Tạo database '{db_name}' thành công"))
        container = db.create_container(id=container_name, partition_key=PartitionKey(path="/id"))
        steps.append((True, f"Tạo container '{container_name}' thành công"))
        item = {"id": "1", "val": "hello"}
        container.create_item(item)
        steps.append((True, "Insert item thành công"))
        items = list(container.query_items(query="SELECT * FROM c WHERE c.id='1'", enable_cross_partition_query=True))
        if items and items[0]["val"] == "hello":
            steps.append((True, f"Query thành công: {items[0]['val']}"))
        else:
            steps.append((False, "Query thất bại!"))
        container.delete_item(item="1", partition_key="1")
        steps.append((True, "Xóa item thành công"))
        db.delete_container(container_name)
        steps.append((True, "Xóa container thành công"))
        client.delete_database(db_name)
        steps.append((True, "Xóa database thành công"))
    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_cosmos_with_credential(cosmos_account, credential, database_name="testdb"):
    """Test Cosmos DB sử dụng Azure credential"""
    steps = []
    try:
        client = get_cosmos_client_with_credential(cosmos_account, credential)
        container_name = "testcontainer"

        # Tạo database
        database = client.create_database_if_not_exists(id=database_name)
        steps.append((True, f"Tạo/kiểm tra database '{database_name}' thành công"))

        # Tạo container
        container = database.create_container_if_not_exists(
            id=container_name,
            partition_key=PartitionKey(path="/id"),
            offer_throughput=400
        )
        steps.append((True, f"Tạo/kiểm tra container '{container_name}' thành công"))

        # Thêm item
        item_id = str(uuid.uuid4())
        item = {"id": item_id, "name": "test", "value": uuid.uuid4().hex}
        container.create_item(body=item)
        steps.append((True, f"Tạo item '{item_id}' thành công"))

        # Đọc item
        read_item = container.read_item(item=item_id, partition_key=item_id)
        if read_item["value"] == item["value"]:
            steps.append((True, "Đọc item thành công"))
        else:
            steps.append((False, "Giá trị item không khớp!"))

        # Xóa item
        container.delete_item(item=item_id, partition_key=item_id)
        steps.append((True, "Xóa item thành công"))

    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_blob_with_credential(storage_account, credential):
    """Test Blob Storage sử dụng Azure credential"""
    steps = []
    container_name = f"testct{uuid.uuid4().hex[:6]}"
    blob_name = "testfile.txt"
    data = b"hello azure blob"
    try:
        client = get_blob_client_with_credential(storage_account, credential)

        # Tạo container
        container_client = client.create_container(container_name)
        steps.append((True, f"Tạo container '{container_name}' thành công"))

        # Upload blob
        blob_client = client.get_blob_client(container=container_name, blob=blob_name)
        blob_client.upload_blob(data, overwrite=True)
        steps.append((True, f"Upload blob '{blob_name}' thành công"))

        # Download blob
        downloaded = blob_client.download_blob().readall()
        if downloaded == data:
            steps.append((True, "Download blob thành công"))
        else:
            steps.append((False, "Dữ liệu blob không khớp!"))

        # Xóa blob
        blob_client.delete_blob()
        steps.append((True, "Xóa blob thành công"))

        # Xóa container
        container_client.delete_container()
        steps.append((True, "Xóa container thành công"))

    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_sql_with_credential(sql_server, sql_database):
    """Test SQL Database sử dụng Azure AD authentication"""
    steps = []
    table_name = f"testtbl{uuid.uuid4().hex[:6]}"
    try:
        conn = create_sql_connection_with_credential(sql_server, sql_database)
        cursor = conn.cursor()
        steps.append((True, "Kết nối SQL Database thành công"))

        # Tạo bảng
        cursor.execute(f"CREATE TABLE {table_name} (id INT IDENTITY(1,1) PRIMARY KEY, val NVARCHAR(100))")
        steps.append((True, f"Tạo bảng '{table_name}' thành công"))

        # Insert dữ liệu
        test_value = uuid.uuid4().hex
        cursor.execute(f"INSERT INTO {table_name} (val) VALUES (?)", (test_value,))
        conn.commit()
        steps.append((True, "Insert dữ liệu thành công"))

        # Select dữ liệu
        cursor.execute(f"SELECT val FROM {table_name} WHERE val = ?", (test_value,))
        result = cursor.fetchone()
        if result and result[0] == test_value:
            steps.append((True, "Select dữ liệu thành công"))
        else:
            steps.append((False, "Dữ liệu không khớp!"))

        # Xóa bảng
        cursor.execute(f"DROP TABLE {table_name}")
        conn.commit()
        steps.append((True, "Xóa bảng thành công"))

        conn.close()

    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_blob_full(connection_string):
    steps = []
    container_name = f"testct{uuid.uuid4().hex[:6]}"
    blob_name = "testfile.txt"
    data = b"hello azure blob"
    try:
        client = BlobServiceClient.from_connection_string(connection_string)
        container = client.create_container(container_name)
        steps.append((True, f"Tạo container '{container_name}' thành công"))
        container_client = client.get_container_client(container_name)
        container_client.upload_blob(blob_name, data)
        steps.append((True, "Upload blob thành công"))
        blob_data = container_client.download_blob(blob_name).readall()
        if blob_data == data:
            steps.append((True, "Download blob thành công"))
        else:
            steps.append((False, "Dữ liệu blob không khớp!"))
        container_client.delete_blob(blob_name)
        steps.append((True, "Xóa blob thành công"))
        client.delete_container(container_name)
        steps.append((True, "Xóa container thành công"))
    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_redis_full(redis_connection_string):
    steps = []
    key = f"testkey:{uuid.uuid4().hex[:6]}"
    value = uuid.uuid4().hex
    try:
        # Hỗ trợ Redis với SSH tunnel
        if redis_connection_string.startswith('ssh://'):
            # Parse SSH connection string: ssh://user@host:port
            ssh_parts = redis_connection_string.replace('ssh://', '').split('@')
            if len(ssh_parts) == 2:
                user = ssh_parts[0]
                host_port = ssh_parts[1].split(':')
                host = host_port[0]
                port = int(host_port[1]) if len(host_port) > 1 else 22
                
                # Sử dụng SSH tunnel để kết nối Redis
                import paramiko
                ssh = paramiko.SSHClient()
                ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
                ssh.connect(host, port=port, username=user)
                
                # Tạo Redis connection qua SSH tunnel
                r = redis.Redis(host='localhost', port=6379, decode_responses=True)
                steps.append((True, f"Kết nối Redis qua SSH thành công: {host}:{port}"))
            else:
                steps.append((False, "SSH connection string không hợp lệ"))
                return steps
        else:
            # Kết nối Redis trực tiếp
            r = redis.from_url(redis_connection_string)
            steps.append((True, "Kết nối Redis trực tiếp thành công"))
        
        r.set(key, value)
        steps.append((True, f"Set key '{key}' thành công"))
        val = r.get(key)
        if val and val == value:
            steps.append((True, "Get key thành công"))
        else:
            steps.append((False, "Giá trị key không khớp!"))
        r.delete(key)
        steps.append((True, "Xóa key thành công"))
        
        if redis_connection_string.startswith('ssh://'):
            ssh.close()
            
    except Exception as e:
        steps.append((False, str(e)))
    return steps

def test_acr_full(acr_name, subscription_id, resource_group, credential):
    steps = []
    try:
        acr_client = ContainerRegistryManagementClient(credential, subscription_id)
        registry = acr_client.registries.get(resource_group, acr_name)
        login_server = registry.login_server
        steps.append((True, f"Login server: {login_server}"))
        try:
            props = acr_client.registries.get(resource_group, acr_name)
            steps.append((True, "Có thể truy cập registry properties"))
        except Exception as e:
            steps.append((False, f"Không thể truy cập registry properties: {e}"))
    except Exception as e:
        steps.append((False, str(e)))
    return steps

def list_key_vault_secrets(vault_url, credential):
    try:
        client = SecretClient(vault_url=f"https://{vault_url}/", credential=credential)
        secrets = [s.name for s in client.list_properties_of_secrets()]
        return secrets
    except Exception as e:
        return [str(e)]

def list_sql_tables(connection_string):
    try:
        conn = pyodbc.connect(connection_string, timeout=5)
        cursor = conn.cursor()
        cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE='BASE TABLE'")
        tables = [row[0] for row in cursor.fetchall()]
        conn.close()
        return tables
    except Exception as e:
        return [str(e)]

def list_cosmos_items(endpoint, key, db_name, container_name):
    try:
        client = CosmosClient(f"https://{endpoint}/", key)
        db = client.get_database_client(db_name)
        container = db.get_container_client(container_name)
        items = list(container.read_all_items())
        return items
    except Exception as e:
        return [str(e)]

def list_blob_containers(connection_string):
    try:
        client = BlobServiceClient.from_connection_string(connection_string)
        containers = [c['name'] for c in client.list_containers()]
        return containers
    except Exception as e:
        return [str(e)]

def list_blobs_in_container(connection_string, container_name):
    try:
        client = BlobServiceClient.from_connection_string(connection_string)
        container_client = client.get_container_client(container_name)
        blobs = [b.name for b in container_client.list_blobs()]
        return blobs
    except Exception as e:
        return [str(e)]

def list_acr_images(acr_name, subscription_id, resource_group, credential):
    try:
        acr_client = ContainerRegistryManagementClient(credential, subscription_id)
        repos = acr_client.registries.list_credentials(resource_group, acr_name)
        # This only lists credentials, to list images use REST or Azure SDK for Container Registry (preview)
        # Here, we just return the login server as a placeholder
        registry = acr_client.registries.get(resource_group, acr_name)
        return [registry.login_server]
    except Exception as e:
        return [str(e)]

def list_redis_keys(redis_connection_string, pattern='*'):
    try:
        r = redis.from_url(redis_connection_string)
        keys = r.keys(pattern)
        return [k.decode() for k in keys]
    except Exception as e:
        return [str(e)]

def normalize_host_from_url(value):
    v = (value or '').strip()
    if v.startswith('https://'):
        v = v[len('https://'):]
    if v.startswith('http://'):
        v = v[len('http://'):]
    return v.strip('/')

def get_azure_credential():
    """Lấy Azure credential và kiểm tra xem có hoạt động không"""
    try:
        credential = DefaultAzureCredential()
        # Test credential bằng cách lấy token
        credential.get_token("https://management.azure.com/.default")
        return credential, None
    except Exception as e:
        return None, str(e)

def get_cosmos_client_with_credential(cosmos_account, credential):
    """Tạo Cosmos client sử dụng Azure credential"""
    try:
        endpoint = f"https://{cosmos_account}.documents.azure.com:443/"
        return CosmosClient(endpoint, credential=credential)
    except Exception as e:
        raise Exception(f"Không thể kết nối Cosmos DB: {str(e)}")

def get_blob_client_with_credential(storage_account, credential):
    """Tạo Blob client sử dụng Azure credential"""
    try:
        account_url = f"https://{storage_account}.blob.core.windows.net"
        return BlobServiceClient(account_url=account_url, credential=credential)
    except Exception as e:
        raise Exception(f"Không thể kết nối Blob Storage: {str(e)}")

def get_redis_access_key(redis_name, subscription_id, resource_group, credential):
    """Lấy Redis access key từ Azure Management API"""
    try:
        redis_client = RedisManagementClient(credential, subscription_id)
        keys = redis_client.redis.list_keys(resource_group, redis_name)
        return keys.primary_key
    except Exception as e:
        raise Exception(f"Không thể lấy Redis access key: {str(e)}")

def create_sql_connection_with_credential(sql_server, sql_database):
    """Tạo SQL connection sử dụng Azure AD authentication"""
    try:
        connection_string = (
            f"Driver={{ODBC Driver 18 for SQL Server}};"
            f"Server=tcp:{sql_server},1433;"
            f"Database={sql_database};"
            f"Authentication=ActiveDirectoryDefault;"
            f"Encrypt=yes;"
            f"TrustServerCertificate=no;"
            f"Connection Timeout=30;"
        )
        return pyodbc.connect(connection_string, timeout=5)
    except Exception as e:
        raise Exception(f"Không thể kết nối SQL Database: {str(e)}")

def get_config():
    # Lấy biến môi trường từ .env file (đã được load bởi python-dotenv)
    config = {}

    # Azure Resource Configuration - chỉ cần tên resource, sẽ dùng DefaultAzureCredential
    config["keyvault_name"] = os.environ.get("KEYVAULT_NAME", "") or os.environ.get("KEY_VAULT_NAME", "")
    config["sql_server"] = os.environ.get("SQL_SERVER", "")
    config["sql_database"] = os.environ.get("SQL_DATABASE", "")
    config["cosmos_account"] = os.environ.get("COSMOS_ACCOUNT", "") or os.environ.get("COSMOS_ENDPOINT", "").replace("https://", "").replace(".documents.azure.com", "")
    config["cosmos_database"] = os.environ.get("COSMOS_DATABASE", "") or os.environ.get("COSMOS_DATABASE_NAME", "")
    config["storage_account"] = os.environ.get("STORAGE_ACCOUNT", "") or os.environ.get("BLOB_ACCOUNT", "")
    config["acr_name"] = os.environ.get("ACR_NAME", "")
    config["redis_name"] = os.environ.get("REDIS_NAME", "") or os.environ.get("REDIS_HOST", "").replace(".redis.cache.windows.net", "")

    # Azure Subscription và Resource Group
    config["subscription_id"] = os.environ.get("AZURE_SUBSCRIPTION_ID", "") or os.environ.get("ACR_SUBSCRIPTION", "")
    config["resource_group"] = os.environ.get("AZURE_RESOURCE_GROUP", "") or os.environ.get("ACR_RG", "")

    # Fallback to connection strings nếu có (để tương thích ngược)
    config["sql_connection_string"] = os.environ.get("SQL_CONNECTION_STRING", "")
    config["cosmos_connection_string"] = os.environ.get("COSMOS_CONNECTION_STRING", "")
    config["blob_connection_string"] = os.environ.get("BLOB_CONNECTION_STRING", "")
    config["redis_connection_string"] = os.environ.get("REDIS_CONNECTION_STRING", "")
    
    # Thêm các biến môi trường mới cho Cosmos DB (như trong test_all_services.py)
    config["cosmos_endpoint"] = os.environ.get("COSMOS_ENDPOINT", "")
    config["cosmos_key"] = os.environ.get("COSMOS_KEY", "")
    config["cosmos_database_name"] = os.environ.get("COSMOS_DATABASE_NAME", "")
    
    # Thêm các biến môi trường cho ACR
    config["acr_subscription_id"] = os.environ.get("ACR_SUBSCRIPTION_ID", "")
    config["acr_resource_group"] = os.environ.get("ACR_RESOURCE_GROUP", "")

    # Tạo URLs từ resource names
    if config["keyvault_name"]:
        config["keyvault_url"] = f"{config['keyvault_name']}.vault.azure.net"
    else:
        config["keyvault_url"] = normalize_host_from_url(os.environ.get("KEY_VAULT_URL", "") or os.environ.get("KEY_VAULT_URL", ""))

    # Tạo SQL connection string từ Azure AD authentication nếu có server và database
    if config["sql_server"] and config["sql_database"] and not config["sql_connection_string"]:
        config["sql_connection_string"] = f"Driver={{ODBC Driver 18 for SQL Server}};Server=tcp:{config['sql_server']},1433;Database={config['sql_database']};Authentication=ActiveDirectoryDefault;Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;"

    # Tạo Cosmos connection string từ account name nếu có
    if config["cosmos_account"] and not config["cosmos_connection_string"]:
        config["cosmos_endpoint"] = f"https://{config['cosmos_account']}.documents.azure.com:443/"
        # Sẽ sử dụng DefaultAzureCredential thay vì connection string

    # Tạo Blob storage URL từ account name nếu có
    if config["storage_account"] and not config["blob_connection_string"]:
        config["blob_url"] = f"https://{config['storage_account']}.blob.core.windows.net/"
        # Sẽ sử dụng DefaultAzureCredential thay vì connection string

    # Tạo Redis connection string từ name nếu có
    if config["redis_name"] and not config["redis_connection_string"]:
        config["redis_host"] = f"{config['redis_name']}.redis.cache.windows.net"
        # Redis sẽ cần access key, có thể lấy từ Azure Management API

    return config

# --- HTML Template ---
TEMPLATE = '''
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Azure Connectivity Tester (Flask)</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .card { margin-bottom: 2rem; }
        .service-title { font-size: 1.3rem; font-weight: 600; }
        .result-list { margin-top: 1rem; }
        .azure-status { 
            background: #e9ecef; 
            padding: 1rem; 
            border-radius: 0.375rem; 
            margin-bottom: 2rem; 
            border-left: 4px solid #6c757d;
        }
        .azure-status.success { 
            background: #d1e7dd; 
            border-left-color: #198754; 
        }
        .azure-status.warning { 
            background: #fff3cd; 
            border-left-color: #ffc107; 
        }
    </style>
</head>
<body>
<div class="container py-4">
    <h1 class="mb-4 text-center">🔗 Azure Connectivity Tester (Flask)</h1>
    
    <!-- Azure Authentication Status -->
    <div class="azure-status {% if 'successful' in azure_status %}success{% else %}warning{% endif %}">
        <strong>🔐 Azure Authentication Status:</strong> {{ azure_status }}
    </div>
    
    <div class="row row-cols-1 row-cols-md-2 g-4">
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Key Vault</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="keyvault">
                        <div class="col-md-5">
                            <label class="form-label">Secret Name</label>
                            <input name="keyvault_secret_name" class="form-control">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Secret Value</label>
                            <input name="keyvault_secret_value" class="form-control">
                        </div>
                        <div class="col-md-2 d-grid gap-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Add</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    {% if results_keyvault is not none %}
                        <div class="result-list">
                        {% for msg in results_keyvault %}
                            {% if 'added' in msg or 'Secret' in msg or 'success' in msg or 'thành công' in msg %}
                                <div class="alert alert-success py-2 mb-2">{{ msg }}</div>
                            {% else %}
                                <div class="alert alert-danger py-2 mb-2">{{ msg }}</div>
                            {% endif %}
                        {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Azure SQL Database</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="sql">
                        <div class="col-md-5">
                            <label class="form-label">Table Name</label>
                            <input name="sql_table" class="form-control">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Value</label>
                            <input name="sql_value" class="form-control">
                        </div>
                        <div class="col-md-2 d-grid gap-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Add</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    {% if results_sql is not none %}
                        <div class="result-list">
                        {% for msg in results_sql %}
                            {% if 'Inserted' in msg or 'table' in msg or 'Table' in msg or 'success' in msg or 'thành công' in msg %}
                                <div class="alert alert-success py-2 mb-2">{{ msg }}</div>
                            {% else %}
                                <div class="alert alert-danger py-2 mb-2">{{ msg }}</div>
                            {% endif %}
                        {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Cosmos DB</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="cosmos">
                        <div class="col-md-4">
                            <label class="form-label">DB Name</label>
                            <input name="cosmos_db" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Container Name</label>
                            <input name="cosmos_container" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Item (JSON)</label>
                            <input name="cosmos_item" class="form-control">
                        </div>
                        <div class="col-12 d-grid gap-2 mt-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Add</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    {% if results_cosmos is not none %}
                        <div class="result-list">
                        {% for msg in results_cosmos %}
                            {% if 'Item' in msg or 'success' in msg or 'thành công' in msg %}
                                <div class="alert alert-success py-2 mb-2">{{ msg }}</div>
                            {% else %}
                                <div class="alert alert-danger py-2 mb-2">{{ msg }}</div>
                            {% endif %}
                        {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Blob Storage</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="blob">
                        <div class="col-md-4">
                            <label class="form-label">Container Name</label>
                            <input name="blob_container" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Blob Name</label>
                            <input name="blob_name" class="form-control">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Data</label>
                            <input name="blob_data" class="form-control">
                        </div>
                        <div class="col-12 d-grid gap-2 mt-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Upload</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    {% if results_blob is not none %}
                        <div class="result-list">
                        {% for msg in results_blob %}
                            {% if 'uploaded' in msg or 'success' in msg or 'thành công' in msg %}
                                <div class="alert alert-success py-2 mb-2">{{ msg }}</div>
                            {% else %}
                                <div class="alert alert-danger py-2 mb-2">{{ msg }}</div>
                            {% endif %}
                        {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Azure Container Registry (ACR)</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="acr">
                        <div class="col-12 d-grid gap-2">
                            <button type="submit" name="action" value="list" class="btn btn-secondary">List Images</button>
                        </div>
                    </form>
                    {% if results_acr is not none %}
                        <div class="result-list">
                        {% for msg in results_acr %}
                            <div class="alert alert-info py-2 mb-2">{{ msg }}</div>
                        {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="col">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="service-title mb-2">Azure Redis Cache</div>
                    <form method="post" class="row g-2 align-items-end">
                        <input type="hidden" name="service" value="redis">
                        <div class="col-md-5">
                            <label class="form-label">Key</label>
                            <input name="redis_key" class="form-control">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Value</label>
                            <input name="redis_value" class="form-control">
                        </div>
                        <div class="col-md-2 d-grid gap-2">
                            <button type="submit" name="action" value="add" class="btn btn-primary">Set</button>
                            <button type="submit" name="action" value="list" class="btn btn-secondary mt-1">List</button>
                        </div>
                    </form>
                    {% if results_redis is not none %}
                        <div class="result-list">
                        {% for msg in results_redis %}
                            {% if 'set' in msg or 'success' in msg or 'thành công' in msg %}
                                <div class="alert alert-success py-2 mb-2">{{ msg }}</div>
                            {% else %}
                                <div class="alert alert-danger py-2 mb-2">{{ msg }}</div>
                            {% endif %}
                        {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <footer class="text-center mt-4 mb-2 text-muted">&copy; {{ 2024 }} Azure Connectivity Tester</footer>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
'''

@app.route('/', methods=['GET', 'POST'])
def index():
    results_keyvault = results_sql = results_cosmos = results_blob = results_acr = results_redis = None
    
    # Try to get Azure credentials, but handle gracefully if not available
    credential = None
    azure_auth_error = None
    try:
        credential = DefaultAzureCredential()
        # Test if credential works by trying to get a token
        credential.get_token("https://management.azure.com/.default")
    except Exception as e:
        azure_auth_error = str(e)
        print(f"Azure authentication not available: {e}")
        # Continue without Azure credentials for services that don't need them
    
    CONFIG = get_config()
    
    if request.method == 'POST':
        service = request.form.get('service')
        action = request.form.get('action')
        
        if service == 'keyvault':
            vault_url = CONFIG['keyvault_url']
            if not vault_url:
                results_keyvault = ["⚠️ KEY_VAULT_URL not configured. Please set it in your .env file."]
            elif not credential:
                results_keyvault = [f"⚠️ Azure authentication failed: {azure_auth_error}. Please configure Azure credentials."]
            else:
                if action == 'add':
                    secret_name = request.form.get('keyvault_secret_name')
                    secret_value = request.form.get('keyvault_secret_value')
                    try:
                        client = SecretClient(vault_url=f"https://{vault_url}/", credential=credential)
                        client.set_secret(secret_name, secret_value)
                        results_keyvault = [f"✅ Secret '{secret_name}' added successfully."]
                    except Exception as e:
                        results_keyvault = [f"❌ Error: {str(e)}"]
                elif action == 'list':
                    try:
                        # Sử dụng test_key_vault_full để test và list
                        test_steps = test_key_vault_full(vault_url, credential)
                        if any(not success for success, _ in test_steps):
                            results_keyvault = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            results_keyvault = list_key_vault_secrets(vault_url, credential)
                    except Exception as e:
                        results_keyvault = [f"❌ Error: {str(e)}"]
                        
        elif service == 'sql':
            sql_conn_str = CONFIG['sql_connection_string']
            if not sql_conn_str:
                results_sql = ["⚠️ SQL_CONNECTION_STRING not configured. Please set it in your .env file."]
            else:
                if action == 'add':
                    table = request.form.get('sql_table')
                    value = request.form.get('sql_value')
                    try:
                        # Test kết nối trước
                        test_steps = test_azure_sql_full(sql_conn_str)
                        if any(not success for success, _ in test_steps):
                            results_sql = [f"❌ Kết nối thất bại: {msg}" for success, msg in test_steps if not success]
                        else:
                            conn = pyodbc.connect(sql_conn_str, timeout=10)
                            cursor = conn.cursor()
                            # Kiểm tra bảng tồn tại, nếu chưa thì tạo bảng
                            cursor.execute(f"SELECT COUNT(*) FROM sysobjects WHERE name='{table}' AND xtype='U'")
                            exists = cursor.fetchone()[0]
                            if not exists:
                                cursor.execute(f"CREATE TABLE {table} (id INT IDENTITY(1,1) PRIMARY KEY, val NVARCHAR(100))")
                            cursor.execute(f"INSERT INTO {table} (val) VALUES (?)", (value,))
                            conn.commit()
                            conn.close()
                            results_sql = [f"✅ Inserted '{value}' into table '{table}' successfully."]
                    except Exception as e:
                        results_sql = [f"❌ Error: {str(e)}"]
                elif action == 'list':
                    try:
                        # Test kết nối trước
                        test_steps = test_azure_sql_full(sql_conn_str)
                        if any(not success for success, _ in test_steps):
                            results_sql = [f"❌ Kết nối thất bại: {msg}" for success, msg in test_steps if not success]
                        else:
                            results_sql = list_sql_tables(sql_conn_str)
                    except Exception as e:
                        results_sql = [f"❌ Error: {str(e)}"]
                        
        elif service == 'cosmos':
            # Lấy config Cosmos DB từ nhiều nguồn khác nhau
            cosmos_connection_string = CONFIG.get('cosmos_connection_string', '')
            cosmos_endpoint = CONFIG.get('cosmos_endpoint', '')
            cosmos_key = CONFIG.get('cosmos_key', '')

            # Xử lý kết nối Cosmos DB
            if cosmos_connection_string:
                # Sử dụng connection string (hỗ trợ cả MongoDB và SQL API)
                if action == 'add':
                    db_name = request.form.get('cosmos_db')
                    container_name = request.form.get('cosmos_container')
                    item_json = request.form.get('cosmos_item')
                    try:
                        # Sử dụng hàm test_cosmosdb_full đã được cải tiến
                        test_steps = test_cosmosdb_full(cosmos_connection_string)
                        if any(not success for success, _ in test_steps):
                            # Có lỗi trong test
                            results_cosmos = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            # Test thành công, thực hiện add item
                            import json
                            item = json.loads(item_json)

                            if cosmos_connection_string.startswith('mongodb://'):
                                # MongoDB API
                                from pymongo import MongoClient
                                client = MongoClient(cosmos_connection_string)
                                db = client[db_name]
                                collection = db[container_name]
                                result = collection.insert_one(item)
                                results_cosmos = [f"✅ Inserted item with ID: {result.inserted_id}"]
                                client.close()
                            else:
                                # SQL API
                                conn_parts = dict(part.split('=', 1) for part in cosmos_connection_string.split(';') if '=' in part)
                                endpoint = conn_parts.get('AccountEndpoint', '').replace('https://', '').replace('http://', '')
                                key = conn_parts.get('AccountKey', '')

                                client = CosmosClient(f"https://{endpoint}/", key)
                                db = client.create_database_if_not_exists(db_name)
                                container = db.create_container_if_not_exists(
                                    id=container_name,
                                    partition_key=PartitionKey(path="/id"),
                                    offer_throughput=400
                                )
                                container.create_item(item)
                                results_cosmos = [f"✅ Inserted item into container '{container_name}' successfully."]
                    except Exception as e:
                        results_cosmos = [f"❌ Error: {str(e)}"]
                elif action == 'list':
                    try:
                        # Sử dụng hàm test để kiểm tra kết nối
                        test_steps = test_cosmosdb_full(cosmos_connection_string)
                        if any(not success for success, _ in test_steps):
                            results_cosmos = [f"❌ Connection failed: {msg}" for success, msg in test_steps if not success]
                        else:
                            results_cosmos = ["✅ Cosmos DB connection successful"]
                    except Exception as e:
                        results_cosmos = [f"❌ Error: {str(e)}"]
                    try:
                        conn_parts = dict(part.split('=', 1) for part in cosmos_connection_string.split(';') if '=' in part)
                        endpoint = conn_parts.get('AccountEndpoint', '').replace('https://', '').replace('http://', '')
                        key = conn_parts.get('AccountKey', '')
                    except:
                        results_cosmos = ["❌ Invalid COSMOS_CONNECTION_STRING format. Expected: AccountEndpoint=...;AccountKey=..."]
                        return render_template_string(TEMPLATE, results_keyvault=results_keyvault, results_sql=results_sql, results_cosmos=results_cosmos, results_blob=results_blob, results_acr=results_acr, results_redis=results_redis)
            elif cosmos_endpoint and cosmos_key:
                # Sử dụng endpoint + key
                endpoint = cosmos_endpoint.replace('https://', '').replace('http://', '')
                key = cosmos_key
            else:
                results_cosmos = ["⚠️ Cosmos DB not configured. Please set either COSMOS_CONNECTION_STRING or COSMOS_ENDPOINT + COSMOS_KEY in your .env file."]
                        
        elif service == 'blob':
            blob_conn_str = CONFIG['blob_connection_string']
            if not blob_conn_str:
                results_blob = ["⚠️ BLOB_CONNECTION_STRING not configured. Please set it in your .env file."]
            else:
                if action == 'add':
                    container_name = request.form.get('blob_container')
                    blob_name = request.form.get('blob_name')
                    data = request.form.get('blob_data', '').encode()
                    try:
                        # Sử dụng test_blob_full để test trước
                        test_steps = test_blob_full(blob_conn_str)
                        if any(not success for success, _ in test_steps):
                            results_blob = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            # Nếu test thành công, thực hiện upload
                            client = BlobServiceClient.from_connection_string(blob_conn_str)
                            container_client = client.get_container_client(container_name)
                            try:
                                container_client.create_container()
                            except:
                                pass  # Container đã tồn tại
                            container_client.upload_blob(blob_name, data, overwrite=True)
                            results_blob = [f"✅ Blob '{blob_name}' uploaded to '{container_name}' successfully."]
                    except Exception as e:
                        results_blob = [f"❌ Error: {str(e)}"]
                elif action == 'list':
                    try:
                        # Sử dụng test_blob_full để test và list
                        test_steps = test_blob_full(blob_conn_str)
                        if any(not success for success, _ in test_steps):
                            results_blob = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            results_blob = ["✅ Blob Storage connection successful"]
                    except Exception as e:
                        results_blob = [f"❌ Error: {str(e)}"]
                        
        elif service == 'acr':
            acr_name = CONFIG['acr_name']
            acr_subscription = CONFIG.get('acr_subscription_id', '') or CONFIG.get('acr_subscription', '')
            acr_rg = CONFIG.get('acr_resource_group', '') or CONFIG.get('acr_rg', '')
            if not all([acr_name, acr_subscription, acr_rg]):
                results_acr = ["⚠️ ACR_NAME, ACR_SUBSCRIPTION_ID, or ACR_RESOURCE_GROUP not configured. Please set them in your .env file."]
            elif not credential:
                results_acr = [f"⚠️ Azure authentication failed: {azure_auth_error}. Please configure Azure credentials."]
            else:
                if action == 'list':
                    try:
                        # Sử dụng test_acr_full để test và list
                        test_steps = test_acr_full(acr_name, acr_subscription, acr_rg, credential)
                        if any(not success for success, _ in test_steps):
                            results_acr = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            results_acr = ["✅ Container Registry connection successful"]
                    except Exception as e:
                        results_acr = [f"❌ Error: {str(e)}"]
                        
        elif service == 'redis':
            redis_conn_str = CONFIG['redis_connection_string']
            if not redis_conn_str:
                results_redis = ["⚠️ REDIS_CONNECTION_STRING not configured. Please set it in your .env file."]
            else:
                if action == 'add':
                    key = request.form.get('redis_key')
                    value = request.form.get('redis_value')
                    try:
                        # Sử dụng test_redis_full để test trước
                        test_steps = test_redis_full(redis_conn_str)
                        if any(not success for success, _ in test_steps):
                            results_redis = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            # Nếu test thành công, thực hiện set key
                            r = redis.from_url(redis_conn_str)
                            r.set(key, value)
                            results_redis = [f"✅ Key '{key}' set successfully."]
                    except Exception as e:
                        results_redis = [f"❌ Error: {str(e)}"]
                elif action == 'list':
                    try:
                        # Sử dụng test_redis_full để test và list
                        test_steps = test_redis_full(redis_conn_str)
                        if any(not success for success, _ in test_steps):
                            results_redis = [f"❌ {msg}" for success, msg in test_steps if not success]
                        else:
                            results_redis = ["✅ Redis Cache connection successful"]
                    except Exception as e:
                        results_redis = [f"❌ Error: {str(e)}"]
    
    # Add Azure authentication status to the template
    azure_status = "✅ Azure authentication successful" if credential else f"⚠️ Azure authentication failed: {azure_auth_error}"
    
    # Thêm thông tin về các service được cấu hình
    configured_services = []
    if CONFIG.get('sql_connection_string') or (CONFIG.get('sql_server') and CONFIG.get('sql_database')):
        configured_services.append("SQL Database")
    if CONFIG.get('cosmos_connection_string') or (CONFIG.get('cosmos_endpoint') and CONFIG.get('cosmos_key')):
        configured_services.append("Cosmos DB")
    if CONFIG.get('blob_connection_string') or CONFIG.get('storage_account'):
        configured_services.append("Blob Storage")
    if CONFIG.get('redis_connection_string') or CONFIG.get('redis_name'):
        configured_services.append("Redis Cache")
    if CONFIG.get('keyvault_url'):
        configured_services.append("Key Vault")
    if CONFIG.get('acr_name'):
        configured_services.append("Container Registry")
    
    if configured_services:
        azure_status += f" | Configured services: {', '.join(configured_services)}"
    else:
        azure_status += " | No services configured"
    
    return render_template_string(
        TEMPLATE,
        results_keyvault=results_keyvault,
        results_sql=results_sql,
        results_cosmos=results_cosmos,
        results_blob=results_blob,
        results_acr=results_acr,
        results_redis=results_redis,
        azure_status=azure_status
    )

if __name__ == '__main__':
    app.run(debug=True) 
