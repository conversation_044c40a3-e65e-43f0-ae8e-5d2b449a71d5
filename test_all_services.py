#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script test tất cả các Azure services sử dụng các hàm test_*_full từ app.py
"""

import sys
import os
from dotenv import load_dotenv
from azure.identity import DefaultAzureCredential

# Import các hàm test từ app.py
from app import (
    test_azure_sql_full,
    test_cosmosdb_full, 
    test_blob_full,
    test_redis_full,
    test_key_vault_full,
    test_acr_full,
    get_config
)

# Set encoding for Windows console
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())

# Load environment variables
load_dotenv()

def test_cosmosdb_mongodb_api(mongodb_conn_str):
    """Test Cosmos DB với MongoDB API"""
    steps = []
    try:
        from pymongo import MongoClient
        import uuid

        # Tạo MongoDB client
        client = MongoClient(mongodb_conn_str)

        # Test connection
        client.admin.command('ping')
        steps.append((True, "Kết nối MongoDB API thành công"))

        # Tạo database và collection
        db_name = f"testdb{uuid.uuid4().hex[:6]}"
        collection_name = f"testcol{uuid.uuid4().hex[:6]}"

        db = client[db_name]
        collection = db[collection_name]

        # Insert document
        doc = {"_id": "test1", "value": "hello mongodb"}
        result = collection.insert_one(doc)
        steps.append((True, f"Insert document thành công: {result.inserted_id}"))

        # Query document
        found_doc = collection.find_one({"_id": "test1"})
        if found_doc and found_doc["value"] == "hello mongodb":
            steps.append((True, f"Query thành công: {found_doc['value']}"))
        else:
            steps.append((False, "Query thất bại"))

        # Delete document
        collection.delete_one({"_id": "test1"})
        steps.append((True, "Xóa document thành công"))

        # Drop collection và database
        collection.drop()
        steps.append((True, "Xóa collection thành công"))

        client.drop_database(db_name)
        steps.append((True, "Xóa database thành công"))

        client.close()

    except ImportError:
        steps.append((False, "pymongo không được cài đặt. Chạy: pip install pymongo"))
    except Exception as e:
        steps.append((False, str(e)))

    return steps

def convert_mongodb_to_sql_api_connection_string(mongodb_conn_str):
    """Convert MongoDB connection string to SQL API format"""
    try:
        # Parse MongoDB connection string
        # Format: ********************************:port/database?options
        if not mongodb_conn_str.startswith('mongodb://'):
            return None

        # Extract parts
        import re
        pattern = r'mongodb://([^:]+):([^@]+)@([^:]+):(\d+)/\?.*'
        match = re.match(pattern, mongodb_conn_str)

        if not match:
            return None

        _, password, host, _ = match.groups()

        # Convert to SQL API format
        endpoint = f"https://{host.replace('.mongo.cosmos.azure.com', '.documents.azure.com')}/"
        key = password

        return f"AccountEndpoint={endpoint};AccountKey={key};"

    except Exception:
        return None

def print_test_results(service_name, steps):
    """In kết quả test cho một service"""
    print(f"\n{'='*60}")
    print(f"TEST {service_name.upper()}")
    print(f"{'='*60}")
    
    success_count = 0
    total_count = len(steps)
    
    for i, (success, message) in enumerate(steps, 1):
        status = "[SUCCESS]" if success else "[ERROR]"
        print(f"{i:2d}. {status} {message}")
        if success:
            success_count += 1
    
    print(f"\nKet qua: {success_count}/{total_count} buoc thanh cong")
    
    if success_count == total_count:
        print(f"🎉 {service_name} HOAT DONG HOAN HAO!")
    elif success_count > 0:
        print(f"⚠️ {service_name} hoat dong mot phan")
    else:
        print(f"❌ {service_name} KHONG HOAT DONG")
    
    return success_count == total_count

def test_all_services():
    """Test tất cả các Azure services"""

    print("AZURE SERVICES CONNECTIVITY TEST")
    print("Dung cac ham test_*_full tu app.py")
    print("=" * 80)

    # Lấy config trực tiếp từ environment variables
    config = {
        'sql_connection_string': os.getenv('SQL_CONNECTION_STRING', ''),
        'cosmos_connection_string': os.getenv('COSMOS_CONNECTION_STRING', ''),
        'cosmos_endpoint': os.getenv('COSMOS_ENDPOINT', ''),
        'cosmos_key': os.getenv('COSMOS_KEY', ''),
        'cosmos_database_name': os.getenv('COSMOS_DATABASE_NAME', ''),
        'blob_connection_string': os.getenv('BLOB_CONNECTION_STRING', ''),
        'redis_connection_string': os.getenv('REDIS_CONNECTION_STRING', ''),
        'keyvault_url': os.getenv('KEY_VAULT_URL', ''),
        'acr_name': os.getenv('ACR_NAME', ''),
        'acr_subscription_id': os.getenv('ACR_SUBSCRIPTION_ID', ''),
        'acr_resource_group': os.getenv('ACR_RESOURCE_GROUP', '')
    }
    
    # Khởi tạo Azure credential
    credential = None
    try:
        credential = DefaultAzureCredential()
        # Test credential
        credential.get_token("https://management.azure.com/.default")
        print("[INFO] Azure authentication thanh cong")
    except Exception as e:
        print(f"[WARNING] Azure authentication that bai: {e}")
        print("[INFO] Se chi test cac service dung connection string")
    
    results = {}
    
    # 1. Test Azure SQL Database
    if config.get('sql_connection_string'):
        print(f"\n[INFO] Testing Azure SQL Database...")
        try:
            steps = test_azure_sql_full(config['sql_connection_string'])
            results['SQL Database'] = print_test_results('Azure SQL Database', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test SQL Database: {e}")
            results['SQL Database'] = False
    else:
        print(f"\n[SKIP] Azure SQL Database - Khong co connection string")
        results['SQL Database'] = None
    
    # 2. Test Cosmos DB
    cosmos_connection_string = config.get('cosmos_connection_string', '')
    cosmos_endpoint = config.get('cosmos_endpoint', '')
    cosmos_key = config.get('cosmos_key', '')

    # Ưu tiên sử dụng connection string nếu có
    if cosmos_connection_string:
        print(f"\n[INFO] Testing Cosmos DB (using connection string)...")
        try:
            # Nếu là MongoDB format, sử dụng MongoDB API test
            if cosmos_connection_string.startswith('mongodb://'):
                print("[INFO] Using MongoDB API test...")
                steps = test_cosmosdb_mongodb_api(cosmos_connection_string)
            else:
                # Assume it's SQL API format
                print("[INFO] Using SQL API test...")
                steps = test_cosmosdb_full(cosmos_connection_string)

            results['Cosmos DB'] = print_test_results('Cosmos DB', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test Cosmos DB: {e}")
            results['Cosmos DB'] = False
    elif cosmos_endpoint and cosmos_key:
        print(f"\n[INFO] Testing Cosmos DB (using endpoint + key)...")
        try:
            # Tạo connection string cho Cosmos DB
            if not cosmos_endpoint.startswith('https://'):
                cosmos_endpoint = f"https://{cosmos_endpoint}"
            cosmos_conn_str = f"AccountEndpoint={cosmos_endpoint};AccountKey={cosmos_key};"
            steps = test_cosmosdb_full(cosmos_conn_str)
            results['Cosmos DB'] = print_test_results('Cosmos DB', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test Cosmos DB: {e}")
            results['Cosmos DB'] = False
    else:
        print(f"\n[SKIP] Cosmos DB - Khong co connection string hoac endpoint/key")
        print(f"  Connection String: {'Co' if cosmos_connection_string else 'Khong co'}")
        print(f"  Endpoint: {'Co' if cosmos_endpoint else 'Khong co'}")
        print(f"  Key: {'Co' if cosmos_key else 'Khong co'}")
        results['Cosmos DB'] = None
    
    # 3. Test Blob Storage
    if config.get('blob_connection_string'):
        print(f"\n[INFO] Testing Blob Storage...")
        try:
            steps = test_blob_full(config['blob_connection_string'])
            results['Blob Storage'] = print_test_results('Blob Storage', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test Blob Storage: {e}")
            results['Blob Storage'] = False
    else:
        print(f"\n[SKIP] Blob Storage - Khong co connection string")
        results['Blob Storage'] = None
    
    # 4. Test Redis Cache
    if config.get('redis_connection_string'):
        print(f"\n[INFO] Testing Redis Cache...")
        try:
            steps = test_redis_full(config['redis_connection_string'])
            results['Redis Cache'] = print_test_results('Redis Cache', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test Redis Cache: {e}")
            results['Redis Cache'] = False
    else:
        print(f"\n[SKIP] Redis Cache - Khong co connection string")
        results['Redis Cache'] = None
    
    # 5. Test Key Vault
    if credential and config.get('keyvault_url'):
        print(f"\n[INFO] Testing Key Vault...")
        try:
            steps = test_key_vault_full(config['keyvault_url'], credential)
            results['Key Vault'] = print_test_results('Key Vault', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test Key Vault: {e}")
            results['Key Vault'] = False
    else:
        print(f"\n[SKIP] Key Vault - Khong co credential hoac URL")
        results['Key Vault'] = None
    
    # 6. Test Container Registry
    acr_config = {
        'acr_name': config.get('acr_name'),
        'subscription_id': config.get('acr_subscription_id'),
        'resource_group': config.get('acr_resource_group')
    }
    
    if credential and all(acr_config.values()):
        print(f"\n[INFO] Testing Container Registry...")
        try:
            steps = test_acr_full(
                acr_config['acr_name'],
                acr_config['subscription_id'], 
                acr_config['resource_group'],
                credential
            )
            results['Container Registry'] = print_test_results('Container Registry', steps)
        except Exception as e:
            print(f"[ERROR] Loi khi test Container Registry: {e}")
            results['Container Registry'] = False
    else:
        print(f"\n[SKIP] Container Registry - Khong co credential hoac config")
        results['Container Registry'] = None
    
    # Tổng kết
    print(f"\n{'='*80}")
    print("TONG KET KET QUA TEST")
    print(f"{'='*80}")
    
    working_services = []
    failed_services = []
    skipped_services = []
    
    for service, result in results.items():
        if result is True:
            working_services.append(service)
            print(f"✅ {service}: HOAT DONG")
        elif result is False:
            failed_services.append(service)
            print(f"❌ {service}: THAT BAI")
        else:
            skipped_services.append(service)
            print(f"⏭️ {service}: BO QUA")
    
    print(f"\nThong ke:")
    print(f"  - Hoat dong: {len(working_services)}")
    print(f"  - That bai: {len(failed_services)}")
    print(f"  - Bo qua: {len(skipped_services)}")
    
    if failed_services:
        print(f"\nCac service can sua:")
        for service in failed_services:
            print(f"  - {service}")
    
    return results

if __name__ == "__main__":
    test_all_services()
