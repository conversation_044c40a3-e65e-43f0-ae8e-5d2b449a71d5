FROM python:3.10.9-slim

WORKDIR /app

# Cài đặt các thư viện hệ thống cần thiết cho ODBC, Cosmos, Blob, Redis, Flask, Azure SDK
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        dnsutils \
        curl \
        unixodbc \
        unixodbc-dev \
        odbcinst \
        gnupg \
        gcc \
        g++ \
        python3-dev \
        openssh-client && \
    curl -fsSL https://packages.microsoft.com/keys/microsoft.asc \
        | gpg --dearmor \
        -o /usr/share/keyrings/microsoft-prod.gpg && \
    curl https://packages.microsoft.com/config/debian/12/prod.list \
        > /etc/apt/sources.list.d/mssql-release.list && \
    apt-get update && \
    ACCEPT_EULA=Y apt-get install -y --no-install-recommends msodbcsql18 && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy requirements và cài đặt Python packages
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy file .env trước (nếu có)
COPY .env* ./

# Copy toàn bộ mã nguồn
COPY . .

# Thiết lập biến môi trường mặc định cho Flask
ENV FLASK_APP=app.py
ENV FLASK_RUN_HOST=0.0.0.0
ENV FLASK_ENV=production
ENV FLASK_DEBUG=False

EXPOSE 5000

# Chạy Flask app với python-dotenv để load .env (nếu có)
CMD ["python", "-c", "from dotenv import load_dotenv; load_dotenv(); import app; app.app.run(host='0.0.0.0', port=5000)"]